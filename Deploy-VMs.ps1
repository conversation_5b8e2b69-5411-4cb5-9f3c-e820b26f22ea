<#
.SYNOPSIS
    Automated VM Deployment Script for VMware vSphere

.DESCRIPTION
    This script reads VM specifications from a CSV file and creates VMs automatically
    in VMware vSphere environment. It includes error handling, logging, and best practices
    for bulk VM deployment.

.PARAMETER CSVPath
    Path to the CSV file containing VM specifications

.PARAMETER vCenterServer
    vCenter Server FQDN or IP address

.PARAMETER LogPath
    Path for log file output (optional)

.EXAMPLE
    .\Deploy-VMs.ps1 -CSVPath ".\VM-Deployment-Template.csv" -vCenterServer "vcenter.domain.com"

.NOTES
    Author: VMware Administrator
    Version: 1.0
    Requires: VMware PowerCLI module
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [string]$CSVPath,

    [Parameter(Mandatory = $true)]
    [string]$vCenterServer,

    [Parameter(Mandatory = $false)]
    [string]$LogPath = ""
)

# Function to initialize log directory and path
function Initialize-LogPath {
    param([string]$CustomLogPath)

    # If no custom log path provided, create default in logs subfolder
    if ([string]::IsNullOrWhiteSpace($CustomLogPath)) {
        $scriptDir = Split-Path -Parent $MyInvocation.ScriptName
        $logsDir = Join-Path $scriptDir "logs"

        # Create logs directory if it doesn't exist
        if (-not (Test-Path $logsDir)) {
            try {
                New-Item -ItemType Directory -Path $logsDir -Force | Out-Null
                Write-Host "Created logs directory: $logsDir"
            }
            catch {
                Write-Host "Warning: Could not create logs directory. Using script directory instead." -ForegroundColor Yellow
                $logsDir = $scriptDir
            }
        }

        $logFileName = "VM-Deployment-$(Get-Date -Format 'yyyyMMdd-HHmmss').log"
        return Join-Path $logsDir $logFileName
    }
    else {
        # Use custom path, but ensure directory exists
        $logDir = Split-Path -Parent $CustomLogPath
        if (-not (Test-Path $logDir)) {
            try {
                New-Item -ItemType Directory -Path $logDir -Force | Out-Null
            }
            catch {
                throw "Could not create log directory: $logDir"
            }
        }
        return $CustomLogPath
    }
}

# Initialize logging function
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
    Add-Content -Path $LogPath -Value $logEntry
}

# Function to validate prerequisites
function Test-Prerequisites {
    Write-Log "Checking prerequisites..."

    # Check if CSV file exists
    if (-not (Test-Path $CSVPath)) {
        Write-Log "CSV file not found: $CSVPath" "ERROR"
        exit 1
    }

    Write-Log "Prerequisites check completed successfully"
}

# Function to connect to vCenter
function Connect-vCenterServer {
    param([string]$Server)

    Write-Log "Connecting to vCenter Server: $Server"

    try {
        # Prompt for credentials
        $credential = Get-Credential -Message "Enter vCenter Server credentials"

        # Connect to vCenter
        $connection = Connect-VIServer -Server $Server -Credential $credential -ErrorAction Stop
        Write-Log "Successfully connected to vCenter Server: $($connection.Name)"
        return $connection
    }
    catch {
        Write-Log "Failed to connect to vCenter Server: $($_.Exception.Message)" "ERROR"
        exit 1
    }
}

# Function to validate VM specifications
function Test-VMSpecifications {
    param([array]$VMSpecs)

    Write-Log "Validating VM specifications..."
    $validationErrors = @()

    foreach ($vm in $VMSpecs) {
        # Ensure numeric fields are integers
        $vm.CPUCount = [int]$vm.CPUCount
        $vm.MemoryGB = [int]$vm.MemoryGB
        $vm.DiskSizeGB = [int]$vm.DiskSizeGB

        # Check required fields
        $requiredFields = @('VMName', 'CPUCount', 'MemoryGB', 'DiskSizeGB', 'ClusterName', 'DatastoreName', 'NetworkName', 'Template')
        foreach ($field in $requiredFields) {
            if ([string]::IsNullOrWhiteSpace($vm.$field)) {
                $validationErrors += "VM '$($vm.VMName)': Missing required field '$field'"
            }
        }

        # Validate numeric fields
        if ($vm.CPUCount -lt 1 -or $vm.CPUCount -gt 128) {
            $validationErrors += "VM '$($vm.VMName)': Invalid CPU count (must be 1-128)"
        }
        if ($vm.MemoryGB -lt 1 -or $vm.MemoryGB -gt 1024) {
            $validationErrors += "VM '$($vm.VMName)': Invalid memory size (must be 1-1024 GB)"
        }
        if ($vm.DiskSizeGB -lt 10) {
            $validationErrors += "VM '$($vm.VMName)': Invalid disk size (must be at least 10 GB)"
        }
    }

    if ($validationErrors.Count -gt 0) {
        Write-Log "Validation errors found:" "ERROR"
        foreach ($error in $validationErrors) {
            Write-Log $error "ERROR"
        }
        exit 1
    }

    Write-Log "VM specifications validation completed successfully"
}

# Function to create a single VM
function New-VMFromSpec {
    param([object]$VMSpec)

    Write-Log "Creating VM: $($VMSpec.VMName)"

    try {
        # Get cluster
        $cluster = Get-Cluster -Name $VMSpec.ClusterName -ErrorAction Stop
        Write-Log "Found cluster: $($cluster.Name)"

        # Get datastore
        $datastore = Get-Datastore -Name $VMSpec.DatastoreName -ErrorAction Stop
        Write-Log "Found datastore: $($datastore.Name) (Free Space: $([math]::Round($datastore.FreeSpaceGB, 2)) GB)"

        # Check if datastore has enough space
        if ($datastore.FreeSpaceGB -lt ($VMSpec.DiskSizeGB + 10)) {
            throw "Insufficient space on datastore $($datastore.Name)"
        }

        # Get network
        $network = Get-VirtualPortGroup -Name $VMSpec.NetworkName -ErrorAction Stop
        Write-Log "Found network: $($network.Name)"

        # Get template
        $template = Get-Template -Name $VMSpec.Template -ErrorAction Stop
        Write-Log "Found template: $($template.Name)"

        # Check if VM already exists
        $existingVM = Get-VM -Name $VMSpec.VMName -ErrorAction SilentlyContinue
        if ($existingVM) {
            Write-Log "VM '$($VMSpec.VMName)' already exists. Skipping..." "WARNING"
            return $false
        }

        # Create VM from template
        $newVMParams = @{
            Name = $VMSpec.VMName
            Template = $template
            ResourcePool = $cluster
            Datastore = $datastore
            ErrorAction = 'Stop'
        }

        $vm = New-VM @newVMParams
        Write-Log "VM '$($VMSpec.VMName)' created successfully"

        # Configure VM specifications
        Write-Log "Configuring VM specifications for: $($VMSpec.VMName)"

        # Set CPU and Memory
        $vm | Set-VM -NumCpu $VMSpec.CPUCount -MemoryGB $VMSpec.MemoryGB -Confirm:$false -ErrorAction Stop
        Write-Log "Set CPU: $($VMSpec.CPUCount), Memory: $($VMSpec.MemoryGB) GB"

        # Configure network adapter
        $vm | Get-NetworkAdapter | Set-NetworkAdapter -NetworkName $VMSpec.NetworkName -Confirm:$false -ErrorAction Stop
        Write-Log "Network adapter configured: $($VMSpec.NetworkName)"

        # Resize hard disk if needed
        $currentDisk = $vm | Get-HardDisk | Select-Object -First 1
        if ($currentDisk.CapacityGB -lt $VMSpec.DiskSizeGB) {
            $currentDisk | Set-HardDisk -CapacityGB $VMSpec.DiskSizeGB -Confirm:$false -ErrorAction Stop
            Write-Log "Hard disk resized to: $($VMSpec.DiskSizeGB) GB"
        }

        # Set VM folder if specified
        if (-not [string]::IsNullOrWhiteSpace($VMSpec.Folder)) {
            $folder = Get-Folder -Name $VMSpec.Folder -Type VM -ErrorAction SilentlyContinue
            if ($folder) {
                $vm | Move-VM -Destination $folder -ErrorAction Stop
                Write-Log "VM moved to folder: $($VMSpec.Folder)"
            }
        }

        # Set notes if specified
        if (-not [string]::IsNullOrWhiteSpace($VMSpec.Notes)) {
            $vm | Set-VM -Notes $VMSpec.Notes -Confirm:$false -ErrorAction Stop
            Write-Log "VM notes updated"
        }

        Write-Log "VM '$($VMSpec.VMName)' configuration completed successfully" "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Failed to create VM '$($VMSpec.VMName)': $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function to set VM guest OS configuration
function Set-VMGuestConfig {
    param(
        [VMware.VimAutomation.ViCore.Types.V1.Inventory.VirtualMachine]$VM,
        [System.Management.Automation.PSCredential]$GuestCredential,
        [string]$NewComputerName,
        [string]$StaticIP,
        [string]$PrefixLength,
        [string]$Gateway,
        [string[]]$DNS
    )
    # Ensure VM is powered on
    if ($VM.PowerState -ne 'PoweredOn') {
        Write-Log "Powering on VM: $($VM.Name)"
        Start-VM -VM $VM -Confirm:$false | Out-Null
        # Wait for VM to be powered on
        $timeout = (Get-Date).AddMinutes(5)
        do {
            Start-Sleep -Seconds 10
            $VM = Get-VM -Id $VM.Id
        } while ($VM.PowerState -ne 'PoweredOn' -and (Get-Date) -lt $timeout)
        if ($VM.PowerState -ne 'PoweredOn') {
            Write-Log "VM '$($VM.Name)' failed to power on in time." "ERROR"
            throw "VM failed to power on."
        }
        Write-Log "VM '$($VM.Name)' is now powered on."
    }
    # Wait 3 minutes after power on before customization
    Write-Log "Waiting 3 minutes after power on before guest customization on VM: $($VM.Name)"
    Start-Sleep -Seconds 180
    Write-Log "Setting static IP and DNS on VM: $($VM.Name)"
    # Convert DNS array to comma-separated string for script execution
    $dnsString = ($DNS | ForEach-Object { "'$_'" }) -join ','

    $setIPScript = @"
# Find the Ethernet1 network adapter
`$iface = Get-NetAdapter | Where-Object { `$_.Name -eq 'Ethernet1' -and `$_.Status -eq 'Up' }

if (`$null -ne `$iface) {
    Write-Host "Found network adapter: `$(`$iface.Name)"

    # Remove existing IP configuration and disable DHCP
    try {
        Write-Host "Removing existing IP configuration..."
        Remove-NetIPAddress -InterfaceAlias `$iface.Name -Confirm:`$false -ErrorAction SilentlyContinue
        Remove-NetRoute -InterfaceAlias `$iface.Name -Confirm:`$false -ErrorAction SilentlyContinue
        Set-NetIPInterface -InterfaceAlias `$iface.Name -Dhcp Disabled -ErrorAction SilentlyContinue
    } catch {
        Write-Host "Warning: Could not fully remove existing IP configuration: `$(`$_.Exception.Message)"
    }

    # Set static IP address
    try {
        Write-Host "Setting static IP address: $StaticIP/$PrefixLength"
        New-NetIPAddress -InterfaceAlias `$iface.Name -IPAddress '$StaticIP' -PrefixLength $PrefixLength -DefaultGateway '$Gateway' -ErrorAction Stop
        Write-Host "Static IP address configured successfully"
    } catch {
        Write-Host "Error setting static IP: `$(`$_.Exception.Message)"
        throw
    }

    # Set DNS servers
    try {
        Write-Host "Setting DNS servers: $($DNS -join ', ')"
        `$dnsServers = @($dnsString)
        Set-DnsClientServerAddress -InterfaceAlias `$iface.Name -ServerAddresses `$dnsServers -ErrorAction Stop
        Write-Host "DNS servers configured successfully"
    } catch {
        Write-Host "Error setting DNS servers: `$(`$_.Exception.Message)"
        throw
    }

    # Verify configuration
    `$ipConfig = Get-NetIPAddress -InterfaceAlias `$iface.Name -AddressFamily IPv4 -ErrorAction SilentlyContinue
    `$dnsConfig = Get-DnsClientServerAddress -InterfaceAlias `$iface.Name -AddressFamily IPv4 -ErrorAction SilentlyContinue

    if (`$ipConfig) {
        Write-Host "IP Configuration verified: `$(`$ipConfig.IPAddress)/`$(`$ipConfig.PrefixLength)"
    }
    if (`$dnsConfig) {
        Write-Host "DNS Configuration verified: `$(`$dnsConfig.ServerAddresses -join ', ')"
    }
} else {
    Write-Host 'Ethernet1 adapter not found or not up.'
    throw 'Ethernet1 adapter not found or not up.'
}
"@
    try {
        $scriptResult = Invoke-VMScript -VM $VM -ScriptText $setIPScript -GuestCredential $GuestCredential -ScriptType PowerShell -ErrorAction Stop
        Write-Log "Network configuration script executed successfully"
        Write-Log "Script output: $($scriptResult.ScriptOutput)"
        Write-Log "Static IP and DNS configuration completed"
    }
    catch {
        Write-Log "Failed to execute network configuration script: $($_.Exception.Message)" "ERROR"
        throw
    }
    Write-Log "Renaming computer to $NewComputerName on VM: $($VM.Name)"
    $renameScript = @"
try {
    Write-Host "Renaming computer to '$NewComputerName'..."
    Rename-Computer -NewName '$NewComputerName' -Force -ErrorAction Stop
    Write-Host "Computer renamed successfully to '$NewComputerName'"
} catch {
    Write-Host "Error renaming computer: `$(`$_.Exception.Message)"
    throw
}
"@
    try {
        $renameResult = Invoke-VMScript -VM $VM -ScriptText $renameScript -GuestCredential $GuestCredential -ScriptType PowerShell -ErrorAction Stop
        Write-Log "Computer rename script executed successfully"
        Write-Log "Rename script output: $($renameResult.ScriptOutput)"
        Write-Log "Computer renamed to: $NewComputerName"
    }
    catch {
        Write-Log "Failed to rename computer: $($_.Exception.Message)" "ERROR"
        throw
    }

    Write-Log "Rebooting VM: $($VM.Name)"
    $rebootScript = @"
try {
    Write-Host "Initiating system reboot..."
    Restart-Computer -Force -ErrorAction Stop
} catch {
    Write-Host "Error initiating reboot: `$(`$_.Exception.Message)"
    throw
}
"@
    # Always attempt the reboot, but do not treat failure as a script failure
    try {
        Invoke-VMScript -VM $VM -ScriptText $rebootScript -GuestCredential $GuestCredential -ScriptType PowerShell -ErrorAction Stop
        Write-Log "Reboot command executed successfully"
        Write-Log "VM reboot initiated"
    } catch {
        Write-Log "Reboot command may have failed or guest agent is unavailable, but continuing to next VM..." "WARNING"
    }
    # Wait 60 seconds to allow the guest agent to restart
    Write-Log "Waiting 60 seconds after reboot to allow guest agent to restart..."
    Start-Sleep -Seconds 60
    # Do not throw or return failure for reboot step
}

# Main execution
try {
    # Initialize log path first
    $LogPath = Initialize-LogPath -CustomLogPath $LogPath

    Write-Log "Starting VM deployment process..."
    Write-Log "Log file: $LogPath"

    # Check prerequisites
    Test-Prerequisites

    # Connect to vCenter
    $vCenterConnection = Connect-vCenterServer -Server $vCenterServer

    # Read CSV file
    Write-Log "Reading VM specifications from: $CSVPath"
    $vmSpecs = Import-Csv -Path $CSVPath
    Write-Log "Found $($vmSpecs.Count) VM specifications"

    # Validate specifications
    Test-VMSpecifications -VMSpecs $vmSpecs

    # Prompt for guest OS credentials once for all VMs
    Write-Log "Prompting for guest OS credentials..."
    Write-Host "`nGuest OS Configuration:" -ForegroundColor Cyan
    Write-Host "The following credentials will be used for guest OS configuration on all VMs." -ForegroundColor Yellow
    Write-Host "Ensure this account has local administrator privileges on the VM template." -ForegroundColor Yellow
    $guestCredential = Get-Credential -Message "Enter local administrator credentials for guest OS configuration"
    Write-Log "Guest OS credentials obtained for user: $($guestCredential.UserName)"

    # Output VM specs for confirmation before deployment
    Write-Host "\nVM specifications to be deployed:" -ForegroundColor Cyan
    foreach ($vm in $vmSpecs) {
        $specTable = @{
            VMName        = $vm.VMName
            CPUCount      = $vm.CPUCount
            MemoryGB      = $vm.MemoryGB
            DiskSizeGB    = $vm.DiskSizeGB
            ClusterName   = $vm.ClusterName
            DatastoreName = $vm.DatastoreName
            NetworkName   = $vm.NetworkName
            GuestOSType   = $vm.GuestOSType
            Template      = $vm.Template
            Folder        = $vm.Folder
            Notes         = $vm.Notes
        }
        $specTable | Format-Table | Out-String | Write-Host
    }

    # Deploy VMs
    $successCount = 0
    $failureCount = 0
    $totalVMs = $vmSpecs.Count

    Write-Log "Starting VM deployment for $totalVMs VMs..."

    $proceed = Read-Host "Ready to deploy $totalVMs VMs. Type Y to continue"
    if ($proceed -ne 'Y') {
        Write-Log "Deployment cancelled by user."
        exit
    }

    foreach ($vmSpec in $vmSpecs) {
        $result = $false
        try {
            $created = New-VMFromSpec -VMSpec $vmSpec
            if ($created) {
                $vm = Get-VM -Name $vmSpec.VMName -ErrorAction Stop
                # Use the shared guest credentials for all VMs
                Write-Log "Configuring guest OS for VM: $($vmSpec.VMName) using credentials for: $($guestCredential.UserName)"
                Set-VMGuestConfig -VM $vm -GuestCredential $guestCredential -NewComputerName $vmSpec.NewComputerName -StaticIP $vmSpec.StaticIP -PrefixLength $vmSpec.PrefixLength -Gateway $vmSpec.Gateway -DNS @($vmSpec.DNS)
                # Wait for reboot and VMware Tools to be ready (accept old versions)
                Write-Log "Waiting for VM '$($vmSpec.NewComputerName)' to reboot and VMware Tools to be ready..."
                $timeout = (Get-Date).AddMinutes(15)
                do {
                    Start-Sleep -Seconds 30
                    $vmAfter = Get-VM -Name $vmSpec.NewComputerName -ErrorAction SilentlyContinue
                    if ($vmAfter -and $vmAfter.PowerState -eq 'PoweredOn') {
                        $toolsStatus = $vmAfter.ExtensionData.Guest.ToolsStatus
                    } else {
                        $toolsStatus = $null
                    }
                    Write-Log "VM '$($vmSpec.NewComputerName)' - Power: $($vmAfter.PowerState), Tools: $toolsStatus"
                    if ((Get-Date) -gt $timeout) {
                        Write-Log "Timeout waiting for VM '$($vmSpec.NewComputerName)' to be ready after reboot." "ERROR"
                        break
                    }
                } while ($null -eq $toolsStatus -or ($toolsStatus -ne 'toolsOk' -and $toolsStatus -ne 'toolsOld'))
                $result = $true
            }
        } catch {
            Write-Log "Failed guest OS configuration for VM '$($vmSpec.VMName)': $($_.Exception.Message)" "ERROR"
            $result = $false
        }
        if ($result) {
            $successCount++
        } else {
            $failureCount++
        }
        # Progress update
        $completed = $successCount + $failureCount
        $percentComplete = [math]::Round(($completed / $totalVMs) * 100, 2)
        Write-Log "Progress: $completed/$totalVMs ($percentComplete%) - Success: $successCount, Failed: $failureCount"
    }

    # Final summary
    Write-Log "VM deployment completed!" "SUCCESS"
    Write-Log "Total VMs: $totalVMs"
    Write-Log "Successfully created: $successCount"
    Write-Log "Failed: $failureCount"

    if ($failureCount -eq 0) {
        Write-Log "All VMs created successfully!" "SUCCESS"
    } else {
        Write-Log "Some VMs failed to create. Check the log for details." "WARNING"
    }
}
catch {
    Write-Log "Script execution failed: $($_.Exception.Message)" "ERROR"
    exit 1
}
finally {
    # Disconnect from vCenter
    if ($vCenterConnection) {
        Disconnect-VIServer -Server $vCenterServer -Confirm:$false
        Write-Log "Disconnected from vCenter Server"
    }

    Write-Log "Script execution completed. Log saved to: $LogPath"
}




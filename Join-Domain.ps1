<#
.SYNOPSIS
    Automated Domain Join Script for VMware VMs

.DESCRIPTION
    This script automates the process of joining newly created VMs to an Active Directory domain.
    It includes secure credential handling and best practices for domain join automation.

.PARAMETER CSVPath
    Path to the CSV file containing VM specifications

.PARAMETER vCenterServer
    vCenter Server FQDN or IP address

.PARAMETER DomainName
    Active Directory domain name to join

.PARAMETER OUPath
    Organizational Unit path for computer objects (optional)

.PARAMETER LogPath
    Path for log file output (optional)

.EXAMPLE
    .\Join-Domain.ps1 -CSVPath ".\VM-Deployment-Template.csv" -vCenterServer "vcenter.domain.com" -DomainName "contoso.com"

.NOTES
    Author: VMware Administrator
    Version: 1.0
    Requires: VMware PowerCLI module, VMware Tools installed on target VMs
    Security: Uses secure credential handling methods
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [string]$CSVPath,

    [Parameter(Mandatory = $true)]
    [string]$vCenterServer,

    [Parameter(Mandatory = $true)]
    [string]$DomainName,

    [Parameter(Mandatory = $false)]
    [string]$OUPath = "",

    [Parameter(Mandatory = $false)]
    [string]$LogPath = "./logs/Domain-Join-$(Get-Date -Format 'yyyyMMdd-HHmmss').log"
)

# Initialize logging function
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
    Add-Content -Path $LogPath -Value $logEntry
}

# Function to get secure credentials
function Get-SecureCredentials {
    Write-Log "Setting up secure credential handling..."

    # Method 1: Interactive credential prompt (recommended for manual execution)
    Write-Log "Please provide domain join credentials..."
    $domainCredential = Get-Credential -Message "Enter domain join account credentials (domain\username)"

    # Method 2: Using Windows Credential Manager (uncomment to use)
    <#
    try {
        # Install CredentialManager module if not present
        if (-not (Get-Module -Name CredentialManager -ListAvailable)) {
            Write-Log "Installing CredentialManager module..."
            Install-Module -Name CredentialManager -Force -Scope CurrentUser
        }

        # Retrieve stored credentials
        $storedCred = Get-StoredCredential -Target "DomainJoinAccount"
        if ($storedCred) {
            $domainCredential = $storedCred
            Write-Log "Retrieved credentials from Windows Credential Manager"
        }
    }
    catch {
        Write-Log "Could not retrieve stored credentials: $($_.Exception.Message)" "WARNING"
    }
    #>

    # Method 3: Azure Key Vault integration (uncomment and configure for production)
    <#
    try {
        # Install Az.KeyVault module if not present
        if (-not (Get-Module -Name Az.KeyVault -ListAvailable)) {
            Install-Module -Name Az.KeyVault -Force -Scope CurrentUser
        }

        # Connect to Azure and retrieve secret
        Connect-AzAccount
        $keyVaultName = "YourKeyVaultName"
        $secretName = "DomainJoinPassword"
        $username = "domain\serviceaccount"

        $secret = Get-AzKeyVaultSecret -VaultName $keyVaultName -Name $secretName
        $securePassword = $secret.SecretValue
        $domainCredential = New-Object System.Management.Automation.PSCredential($username, $securePassword)

        Write-Log "Retrieved credentials from Azure Key Vault"
    }
    catch {
        Write-Log "Could not retrieve credentials from Azure Key Vault: $($_.Exception.Message)" "WARNING"
    }
    #>

    return $domainCredential
}

# Function to wait for VM to be ready
function Wait-VMReady {
    param(
        [VMware.VimAutomation.ViCore.Types.V1.Inventory.VirtualMachine]$VM,
        [int]$TimeoutMinutes = 30
    )

    Write-Log "Waiting for VM '$($VM.Name)' to be ready for domain join..."
    $timeout = (Get-Date).AddMinutes($TimeoutMinutes)
    $warningIssued = $false
    $lastToolsStatus = ""

    do {
        Start-Sleep -Seconds 10
        try {
            $vm = Get-VM -Name $VM.Name -ErrorAction Stop
            $vmTools = $vm.ExtensionData.Guest.ToolsStatus
            $powerState = $vm.PowerState

            # Log status changes only
            if ($vmTools -ne $lastToolsStatus) {
                Write-Log "VM '$($VM.Name)' - Power: $powerState, Tools: $vmTools"
                $lastToolsStatus = $vmTools
            }

            # Check for unrecoverable states
            if ($vmTools -eq "toolsNotInstalled") {
                Write-Log "VMware Tools not installed on VM '$($VM.Name)'" "ERROR"
                return $false
            }

            # Issue warning at halfway point
            if (-not $warningIssued -and (Get-Date) -gt $timeout.AddMinutes(-($TimeoutMinutes/2))) {
                Write-Log "VM '$($VM.Name)' still not ready after $($TimeoutMinutes/2) minutes. Current state: Power=$powerState, Tools=$vmTools" "WARNING"
                $warningIssued = $true
            }

            if ((Get-Date) -gt $timeout) {
                Write-Log "Timeout waiting for VM '$($VM.Name)' to be ready. Final state: Power=$powerState, Tools=$vmTools" "ERROR"
                return $false
            }

            # Accept toolsOk, toolsOld, or toolsRunning as ready states
            $toolsReady = $vmTools -in @("toolsOk", "toolsOld", "toolsRunning")
            $vmReady = ($powerState -eq "PoweredOn") -and $toolsReady

        } catch {
            Write-Log "Error checking VM status: $($_.Exception.Message)" "WARNING"
            # Continue trying on transient errors
        }

    } while (-not $vmReady)

    Write-Log "VM '$($VM.Name)' is ready for domain join (Power: $powerState, Tools: $vmTools)"
    return $true
}

# Function to validate pre-domain join requirements
function Test-DomainJoinPrerequisites {
    param(
        [VMware.VimAutomation.ViCore.Types.V1.Inventory.VirtualMachine]$VM,
        [System.Management.Automation.PSCredential]$GuestCredential,
        [string]$Domain
    )

    Write-Log "Validating domain join prerequisites for VM '$($VM.Name)'..."

    $validationScript = @"
try {
    `$results = @()

    # Test DNS resolution of domain
    try {
        `$dnsResult = Resolve-DnsName -Name '$Domain' -ErrorAction Stop
        `$results += "DNS_OK: Domain '$Domain' resolves successfully"
    } catch {
        `$results += "DNS_FAIL: Cannot resolve domain '$Domain': `$(`$_.Exception.Message)"
    }

    # Test network connectivity to domain controllers
    try {
        `$dcTest = Test-ComputerSecureChannel -Server '$Domain' -ErrorAction SilentlyContinue
        if (`$dcTest) {
            `$results += "DC_REACHABLE: Domain controllers are reachable"
        } else {
            `$results += "DC_CHECK: Domain controller connectivity test inconclusive (may be normal for non-domain machines)"
        }
    } catch {
        `$results += "DC_UNKNOWN: Could not test domain controller connectivity: `$(`$_.Exception.Message)"
    }

    # Check current domain membership
    try {
        `$computerSystem = Get-WmiObject -Class Win32_ComputerSystem -ErrorAction Stop
        if (`$computerSystem.PartOfDomain) {
            `$results += "DOMAIN_STATUS: Already member of domain: `$(`$computerSystem.Domain)"
        } else {
            `$results += "DOMAIN_STATUS: Not currently domain joined (workgroup: `$(`$computerSystem.Workgroup))"
        }
    } catch {
        `$results += "DOMAIN_STATUS_ERROR: Could not check current domain status: `$(`$_.Exception.Message)"
    }

    # Check time synchronization (within 5 minutes is acceptable)
    try {
        `$w32tm = w32tm /query /status 2>&1
        if (`$LASTEXITCODE -eq 0) {
            `$results += "TIME_SYNC: Time service is running"
        } else {
            `$results += "TIME_SYNC_WARN: Time service may not be properly configured"
        }
    } catch {
        `$results += "TIME_SYNC_ERROR: Could not check time synchronization: `$(`$_.Exception.Message)"
    }

    Write-Output (`$results -join "`n")
    exit 0
} catch {
    Write-Output "VALIDATION_ERROR: `$(`$_.Exception.Message)"
    exit 1
}
"@

    try {
        $result = Invoke-VMScript -VM $VM -ScriptText $validationScript -GuestCredential $GuestCredential -ScriptType PowerShell -ErrorAction Stop

        Write-Log "Prerequisites validation results for VM '$($VM.Name)':"
        $result.ScriptOutput -split "`n" | ForEach-Object {
            if ($_ -like "*_FAIL*" -or $_ -like "*_ERROR*") {
                Write-Log "  $_" "WARNING"
            } else {
                Write-Log "  $_" "INFO"
            }
        }

        # Check for critical failures
        if ($result.ScriptOutput -like "*DNS_FAIL*") {
            Write-Log "Critical: DNS resolution failed for domain '$Domain'" "ERROR"
            return $false
        }

        return $true

    } catch {
        Write-Log "Error validating prerequisites for VM '$($VM.Name)': $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function to join VM to domain
function Join-VMToDomain {
    param(
        [VMware.VimAutomation.ViCore.Types.V1.Inventory.VirtualMachine]$VM,
        [System.Management.Automation.PSCredential]$DomainCredential,
        [System.Management.Automation.PSCredential]$GuestCredential,
        [string]$Domain,
        [string]$OU = ""
    )

    Write-Log "Attempting to join VM '$($VM.Name)' to domain '$Domain'..."
    try {
        # Get current boot time for reboot detection
        $bootTimeScript = @"
try {
    `$bootTime = (Get-WmiObject -Class Win32_OperatingSystem).LastBootUpTime
    Write-Output "BOOT_TIME:`$bootTime"
} catch {
    Write-Output "BOOT_TIME_ERROR: Could not get boot time"
}
"@
        $bootTimeResult = Invoke-VMScript -VM $VM -ScriptText $bootTimeScript -GuestCredential $GuestCredential -ScriptType PowerShell -ErrorAction Stop
        $originalBootTime = ($bootTimeResult.ScriptOutput -split "`n" | Where-Object { $_ -like "BOOT_TIME:*" }) -replace "BOOT_TIME:", ""

        # Create a more secure domain join script
        # Note: This still has security implications but is better than plain text
        $encodedPassword = [System.Convert]::ToBase64String([System.Text.Encoding]::Unicode.GetBytes($DomainCredential.GetNetworkCredential().Password))

        $domainJoinScript = @"
# Domain join script to be executed on the guest VM
`$ErrorActionPreference = 'Stop'
try {
    Write-Output "Starting domain join process for domain: $Domain"
    Write-Output "Using account: $($DomainCredential.UserName)"

    # Decode password (still not ideal but better than plain text)
    `$encodedPass = '$encodedPassword'
    `$decodedBytes = [System.Convert]::FromBase64String(`$encodedPass)
    `$password = [System.Text.Encoding]::Unicode.GetString(`$decodedBytes)
    `$securePassword = ConvertTo-SecureString `$password -AsPlainText -Force
    `$domainCred = New-Object System.Management.Automation.PSCredential('$($DomainCredential.UserName)', `$securePassword)

    # Clear password from memory
    `$password = `$null
    `$encodedPass = `$null

    # Join domain
    if ('$OU' -ne '') {
        Write-Output "Joining domain '$Domain' with OU '$OU'..."
        Add-Computer -DomainName '$Domain' -Credential `$domainCred -OUPath '$OU' -Force -Restart -ErrorAction Stop
    } else {
        Write-Output "Joining domain '$Domain'..."
        Add-Computer -DomainName '$Domain' -Credential `$domainCred -Force -Restart -ErrorAction Stop
    }
    Write-Output "DOMAIN_JOIN_SUCCESS: Domain join initiated successfully"
}
catch {
    Write-Output "DOMAIN_JOIN_FAILED: `$(`$_.Exception.Message)"
    exit 1
}
"@
        # Execute domain join script on the VM
        $result = $null
        try {
            $result = Invoke-VMScript -VM $VM -ScriptText $domainJoinScript -GuestCredential $GuestCredential -ScriptType PowerShell -ErrorAction Stop
        } catch {
            $errorMsg = $_.Exception.Message
            if ($errorMsg -like '*guest operations agent could not be contacted*') {
                Write-Log "Guest agent could not be contacted, likely due to VM reboot. Waiting for VM to come back online..." "WARNING"
                # Wait for VM to be ready again (after reboot)
                if (-not (Wait-VMReady -VM $VM -TimeoutMinutes 15)) {
                    Write-Log "VM '$($VM.Name)' did not come back online after reboot." "ERROR"
                    return $false
                }
                Write-Log "VM '$($VM.Name)' is back online. Proceeding to verify domain join..." "INFO"
                return 'VERIFY'
            } else {
                Write-Log "Domain join failed for VM '$($VM.Name)': $errorMsg" "ERROR"
                return $false
            }
        }
        if ($result -and $result.ExitCode -eq 0) {
            Write-Log "Successfully initiated domain join for VM '$($VM.Name)'" "SUCCESS"
            Write-Log "VM will restart to complete domain join process"
            return 'VERIFY'
        } else {
            Write-Log "Domain join failed for VM '$($VM.Name)': $($result.ScriptOutput)" "ERROR"
            return $false
        }
    } catch {
        Write-Log "Error executing domain join for VM '$($VM.Name)': $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function to verify domain join
function Test-DomainJoin {
    param(
        [VMware.VimAutomation.ViCore.Types.V1.Inventory.VirtualMachine]$VM,
        [System.Management.Automation.PSCredential]$GuestCredential,
        [string]$Domain
    )

    Write-Log "Verifying domain join for VM '$($VM.Name)'..."

    try {
        # Wait for VM to restart and come back online
        Start-Sleep -Seconds 10

        # Check domain membership
        $verifyScript = @"
try {
    `$computerSystem = Get-WmiObject -Class Win32_ComputerSystem
    if (`$computerSystem.Domain -eq '$Domain') {
        Write-Output "SUCCESS: Computer is joined to domain $Domain"
        exit 0
    } else {
        Write-Output "FAILED: Computer is in domain `$(`$computerSystem.Domain), expected $Domain"
        exit 1
    }
}
catch {
    Write-Output "ERROR: Could not verify domain membership: `$(`$_.Exception.Message)"
    exit 1
}
"@

        $result = Invoke-VMScript -VM $VM -ScriptText $verifyScript -GuestCredential $GuestCredential -ScriptType PowerShell -ErrorAction Stop

        if ($result.ExitCode -eq 0) {
            Write-Log "Domain join verification successful for VM '$($VM.Name)'" "SUCCESS"
            return $true
        } else {
            Write-Log "Domain join verification failed for VM '$($VM.Name)': $($result.ScriptOutput)" "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "Error verifying domain join for VM '$($VM.Name)': $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Main execution
try {
    Write-Log "Starting domain join process..."
    Write-Log "Log file: $LogPath"

    # Connect to vCenter
    Write-Log "Connecting to vCenter Server: $vCenterServer"
    $credential = Get-Credential -Message "Enter vCenter Server credentials"
    $vCenterConnection = Connect-VIServer -Server $vCenterServer -Credential $credential -ErrorAction Stop
    Write-Log "Successfully connected to vCenter Server"

    # Get secure credentials for domain join
    $domainCredential = Get-SecureCredentials

    # Get guest OS credentials (local administrator)
    Write-Log "Please provide local administrator credentials for guest VMs..."
    $guestCredential = Get-Credential -Message "Enter local administrator credentials for guest VMs"

    # Read CSV file
    Write-Log "Reading VM specifications from: $CSVPath"
    $vmSpecs = Import-Csv -Path $CSVPath
    Write-Log "Found $($vmSpecs.Count) VM specifications"

    # Process each VM
    $successCount = 0
    $failureCount = 0
    $totalVMs = $vmSpecs.Count

    Write-Log "Starting domain join process for $totalVMs VMs..."

    foreach ($vmSpec in $vmSpecs) {
        try {
            # Get VM object
            $vm = Get-VM -Name $vmSpec.VMName -ErrorAction Stop
            Write-Log "Processing VM: $($vm.Name)"

            # Skip Linux VMs (assuming Windows domain join only)
            if ($vmSpec.GuestOSType -like "*linux*" -or $vmSpec.GuestOSType -like "*ubuntu*" -or $vmSpec.GuestOSType -like "*centos*") {
                Write-Log "Skipping Linux VM: $($vm.Name)" "INFO"
                continue
            }

            # Wait for VM to be ready
            if (-not (Wait-VMReady -VM $vm)) {
                Write-Log "VM '$($vm.Name)' not ready, skipping..." "WARNING"
                $failureCount++
                continue
            }

            # Validate domain join prerequisites
            if (-not (Test-DomainJoinPrerequisites -VM $vm -GuestCredential $guestCredential -Domain $DomainName)) {
                Write-Log "VM '$($vm.Name)' failed prerequisite validation, skipping..." "WARNING"
                $failureCount++
                continue
            }

            # Join VM to domain
            $joinResult = Join-VMToDomain -VM $vm -DomainCredential $domainCredential -GuestCredential $guestCredential -Domain $DomainName -OU $OUPath
            if ($joinResult -eq 'VERIFY') {
                # Wait for restart and verify domain join
                Write-Log "Waiting for VM '$($vm.Name)' to restart and come back online before verification..." "INFO"
                if (-not (Wait-VMReady -VM $vm -TimeoutMinutes 15)) {
                    Write-Log "VM '$($vm.Name)' did not come back online after domain join/reboot." "ERROR"
                    $failureCount++
                    continue
                }
                Start-Sleep -Seconds 60  # Give extra time for services to settle
                $verifyResult = Test-DomainJoin -VM $vm -GuestCredential $guestCredential -Domain $DomainName
                if ($verifyResult) {
                    $successCount++
                    Write-Log "VM '$($vm.Name)' successfully joined to domain" "SUCCESS"
                } else {
                    $failureCount++
                    Write-Log "VM '$($vm.Name)' domain join verification failed" "ERROR"
                }
            } elseif ($joinResult) {
                $successCount++
                Write-Log "VM '$($vm.Name)' successfully joined to domain (no reboot detected)" "SUCCESS"
            } else {
                $failureCount++
            }
        } catch {
            Write-Log "Error processing VM '$($vmSpec.VMName)': $($_.Exception.Message)" "ERROR"
            $failureCount++
        }

        # Progress update
        $completed = $successCount + $failureCount
        $percentComplete = [math]::Round(($completed / $totalVMs) * 100, 2)
        Write-Log "Progress: $completed/$totalVMs ($percentComplete%) - Success: $successCount, Failed: $failureCount"
    }

    # Final summary
    Write-Log "Domain join process completed!" "SUCCESS"
    Write-Log "Total VMs processed: $($successCount + $failureCount)"
    Write-Log "Successfully joined: $successCount"
    Write-Log "Failed: $failureCount"
}
catch {
    Write-Log "Script execution failed: $($_.Exception.Message)" "ERROR"
    exit 1
}
finally {
    # Disconnect from vCenter
    if ($vCenterConnection) {
        Disconnect-VIServer -Server $vCenterServer -Confirm:$false
        Write-Log "Disconnected from vCenter Server"
    }

    Write-Log "Script execution completed. Log saved to: $LogPath"
}





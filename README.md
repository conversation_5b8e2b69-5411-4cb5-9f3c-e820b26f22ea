# VMware Automated VM Deployment Solution

This solution provides a comprehensive automated VM deployment system for VMware vSphere environments, including VM creation, static IP/DNS assignment, and domain join automation.

## 📋 Components

1. **VM-Deployment-Template.csv** - CSV template with VM specifications (now includes PrefixLength for subnet)
2. **Deploy-VMs.ps1** - Main VM creation and guest customization script
3. **Join-Domain.ps1** - Domain join automation script
4. **README.md** - This documentation file

## 🔧 Prerequisites

### Software Requirements
- **PowerShell 5.1** or later
- **VMware PowerCLI** module
- **Windows Credential Manager** (optional, for secure credential storage)
- **Azure PowerShell** (optional, for Azure Key Vault integration)

### VMware Environment Requirements
- vCenter Server access with appropriate permissions
- VM templates configured and available
- Clusters, datastores, and networks properly configured
- VMware Tools installed on VM templates

### Permissions Required
- **vCenter Server**: VM creation, configuration, and management permissions
- **Active Directory**: Domain join permissions for the service account
- **Guest OS**: Local administrator access on target VMs

## 🚀 Installation

### 1. Install VMware PowerCLI
```powershell
Install-Module -Name VMware.PowerCLI -Scope CurrentUser
```

### 2. Configure PowerShell Execution Policy
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 3. Optional: Install Additional Modules
```powershell
Install-Module -Name CredentialManager -Scope CurrentUser
Install-Module -Name Az.KeyVault -Scope CurrentUser
```

## 📝 CSV Template Configuration

The `VM-Deployment-Template.csv` file now contains these columns:

| Column        | Description                | Example           |
|--------------|----------------------------|-------------------|
| VMName       | Unique VM name             | VM-WEB-001        |
| CPUCount     | Number of CPU cores        | 2                 |
| MemoryGB     | Memory in GB               | 4                 |
| DiskSizeGB   | Primary disk size in GB    | 60                |
| ClusterName  | Target cluster name        | Production-Cluster|
| DatastoreName| Target datastore name      | VMFS-Datastore-01 |
| NetworkName  | Network/portgroup name     | VM Network        |
| GuestOSType  | Guest OS identifier        | windows2019srv_64Guest |
| Template     | VM template name           | Windows-2019-Template |
| Folder       | VM folder (optional)       | Web Servers       |
| Notes        | VM description (optional)  | Web Server for Production |
| NewComputerName | Computer name in guest   | VM-WEB-001        |
| StaticIP     | Static IPv4 address        | ************      |
| PrefixLength | Subnet prefix length (CIDR)| 24                |
| Gateway      | Default gateway            | **********        |
| DNS          | DNS server(s) (comma separated or single) | *********** |

**Note:** The script now expects `PrefixLength` (e.g., 24 for *************) instead of a subnet mask.

### Example CSV Row
```
VMName,CPUCount,MemoryGB,DiskSizeGB,ClusterName,DatastoreName,NetworkName,GuestOSType,Template,Folder,Notes,NewComputerName,StaticIP,PrefixLength,Gateway,DNS
TEST-SCRIPT-BG,4,32,128,HOBART-CLUSTER,HOB-DS01,DPFEM Servers (13),windows2022srv_64Guest,Template-SVR2022-May25,No Backup,Test powershell deploy,TEST-SCRIPT-BG-01,************,24,**********,***********
```

## 🎯 Usage

### Step 1: Prepare CSV File
- Copy `VM-Deployment-Template.csv` to your working directory
- Fill in all required fields, including `StaticIP`, `PrefixLength`, `Gateway`, and `DNS` for each VM
- Ensure all network, cluster, and datastore names match your environment

### Step 2: Deploy VMs and Set Static IP/DNS
```powershell
# Basic usage (creates VMs, sets static IP, DNS, computer name, and reboots)
./Deploy-VMs.ps1 -CSVPath ".\VM-Deployment-Template.csv" -vCenterServer "vcenter.domain.com"

# With custom log path
./Deploy-VMs.ps1 -CSVPath ".\VMs.csv" -vCenterServer "vcenter.domain.com" -LogPath "C:\Logs\deployment.log"
```
- The script will prompt for guest OS credentials once and use them for all VMs.
- Each VM will be powered on, wait 3 minutes, then have its static IP, DNS, and computer name set, followed by a reboot.
- After reboot, the script waits 60 seconds before continuing to the next VM.
- The script will not fail if the guest agent is unavailable after reboot; it will continue to the next VM.

### Step 3: Join VMs to Domain (Optional)
```powershell
./Join-Domain.ps1 -CSVPath ".\VM-Deployment-Template.csv" -vCenterServer "vcenter.domain.com" -DomainName "contoso.com"

# With specific OU
./Join-Domain.ps1 -CSVPath ".\VMs.csv" -vCenterServer "vcenter.domain.com" -DomainName "contoso.com" -OUPath "OU=Servers,DC=contoso,DC=com"
```

## 🛠️ Script Behavior and Best Practices
- **Static IP/DNS Assignment:** The script sets the static IP and DNS on the `Ethernet1` adapter inside the guest. It disables DHCP and removes old IPs before applying the new configuration.
- **Subnet:** Use the `PrefixLength` column for subnet (e.g., 24 for *************).
- **Reboot Handling:** After setting guest config, the script reboots the VM and waits 60 seconds before continuing. Reboot failures do not stop the script.
- **Multiple VMs:** The script processes all VMs in the CSV, one after another, regardless of reboot/guest agent issues.
- **Logging:** All actions and errors are logged to a timestamped log file in the `logs` folder by default.

## 🔒 Security Best Practices
- Use secure credential handling (prompt, Windows Credential Manager, or Azure Key Vault)
- Use least-privilege service accounts
- Regularly rotate passwords

## 📊 Logging and Monitoring
- Log files are created in the `logs` folder by default
- Log levels: INFO, SUCCESS, WARNING, ERROR
- Real-time progress and error reporting

## 🛠️ Troubleshooting
- See the Troubleshooting section above for common issues
- Review log files for details

## 📈 Performance Considerations
- For large deployments, consider splitting the CSV and running multiple script instances
- Each VM is processed sequentially for reliability

## 🤝 Contributing
- Test changes in a lab environment
- Update documentation
- Follow PowerShell best practices
- Include error handling and logging

## 📄 License
This solution is provided as-is for educational and operational use in VMware environments.

## 📞 Support
- Check the troubleshooting section
- Review log files for detailed error information
- Consult VMware PowerCLI documentation
- Engage with VMware community forums
